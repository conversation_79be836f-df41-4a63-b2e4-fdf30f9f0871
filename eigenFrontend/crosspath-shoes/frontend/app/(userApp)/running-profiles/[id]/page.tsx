"use client";

import React, { useEffect, useState, use, useRef } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { format } from "date-fns";
import { RunningProfile } from "@/lib/types";
import {
  ArrowLeft,
  CalendarDays,
  Settings,
  Star,
  ChevronLeft,
  ChevronRight,
  UserCircle,
  Footprints,
  Video,
  ChevronDown,
  ChevronUp,
  Weight,
  Ruler,
  Target,
  Mountain,
  Clock,
  Activity,
  ShieldAlert,
  Thermometer,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { StorageMedia } from "@/components/ui/storage-media";
import { ProfileLoading } from "@/components/running-profile/ProfileLoading";
import { ProfileError } from "@/components/running-profile/ProfileError";
import { ProfileDetailCard } from "@/components/profile/ProfileDetailCard";

// Animation variants
const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4, ease: "easeOut" },
  },
};

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

// Data item component for displaying profile data
interface DataItemProps {
  label: string;
  value: string | number | null | undefined;
  icon?: React.ElementType;
  unit?: string;
}

const DataItem: React.FC<DataItemProps> = ({
  label,
  value,
  icon: Icon,
  unit = "",
}) => (
  <div className="flex items-start space-x-3 p-3 bg-secondary/30 rounded-lg">
    {Icon && <Icon className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />}
    <div className="flex-grow">
      <p className="text-xs font-medium text-muted-foreground">{label}</p>
      <p className="text-sm font-semibold text-foreground">
        {value !== null && value !== undefined ? `${value}${unit}` : "N/A"}
      </p>
    </div>
  </div>
);

export default function ProfileDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Use React.use to unwrap the params Promise
  const { id } = use(params as Promise<{ id: string }>);
  const router = useRouter();
  const [profile, setProfile] = useState<RunningProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isExpanded, setIsExpanded] = useState(true);
  const sliderRef = useRef<HTMLDivElement>(null);
  // We don't need to track mobile state separately as we're using the same layout for all devices

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch(`/api/running-profiles/${id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch running profile");
        }

        const data = await response.json();
        setProfile(data.runningProfile);
      } catch (err) {
        console.error("Error fetching profile:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load running profile"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  // Handle slider navigation
  const handlePrevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
      if (sliderRef.current) {
        const cardWidth =
          sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide - 1),
          behavior: "smooth",
        });
      }
    }
  };

  const handleNextSlide = () => {
    // We have 5 cards: Runner Info, Foot Measurements, Running Habits, Video Analysis, Wearable Data
    const hasVideoCard = profile?.runningVideoSagittalUrl ? true : false;
    const hasWearableCard =
      profile?.runningPower !== null ||
      profile?.groundContactTime !== null ||
      profile?.verticalOscillationWearable !== null
        ? true
        : false;

    // Calculate actual max slides based on available cards
    let actualMaxSlides = 2; // Runner Info, Foot Measurements, Running Habits are always present (0, 1, 2)
    if (hasVideoCard) actualMaxSlides++;
    if (hasWearableCard) actualMaxSlides++;

    if (currentSlide < actualMaxSlides) {
      setCurrentSlide(currentSlide + 1);
      if (sliderRef.current) {
        const cardWidth =
          sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide + 1),
          behavior: "smooth",
        });
      }
    }
  };

  // Handle scroll events to update current slide
  useEffect(() => {
    const handleScroll = () => {
      if (sliderRef.current) {
        const scrollLeft = sliderRef.current.scrollLeft;
        const cardWidth =
          sliderRef.current.querySelector("div")?.offsetWidth || 0;
        if (cardWidth > 0) {
          const newSlide = Math.round(scrollLeft / cardWidth);
          if (newSlide !== currentSlide) {
            setCurrentSlide(newSlide);
          }
        }
      }
    };

    const sliderElement = sliderRef.current;
    if (sliderElement) {
      sliderElement.addEventListener("scroll", handleScroll);
      return () => {
        sliderElement.removeEventListener("scroll", handleScroll);
      };
    }
  }, [currentSlide]);

  if (loading) {
    return <ProfileLoading />;
  }

  if (error || !profile) {
    return <ProfileError error={error} />;
  }

  return (
    <motion.div
      className="container max-w-6xl mx-auto py-3 px-2 sm:px-4"
      initial="hidden"
      animate="visible"
      variants={staggerVariant}
    >
      {/* Back button */}
      <motion.div variants={fadeInVariant} className="mb-3">
        <div className="flex items-center text-sm text-muted-foreground">
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-auto font-sans hover:bg-transparent hover:text-primary"
            onClick={() => router.push("/running-profiles")}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Profiles
          </Button>
        </div>
      </motion.div>

      {/* Profile Header */}
      <motion.div variants={fadeInVariant} className="mb-4">
        <Card className="border-border/50 overflow-hidden">
          <div className="relative bg-[#23453b] h-32 sm:h-40">
            <div className="absolute bottom-0 left-0 p-4 sm:p-6">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-white">
                  {profile.name}
                </h1>
                {profile.isDefault && (
                  <Badge className="bg-white/20 text-white border-white/10">
                    <Star className="h-3 w-3 mr-1" />
                    Default
                  </Badge>
                )}
              </div>
              <p className="text-xs text-white/80 mt-1 flex items-center">
                <CalendarDays className="h-3 w-3 mr-1" />
                Created: {format(new Date(profile.createdAt), "dd MMM, yyyy")}
              </p>
            </div>

            <div className="absolute top-4 right-4">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 bg-white/10 text-white hover:bg-white/20 backdrop-blur-sm"
                onClick={() =>
                  router.push(`/running-profiles/${profile.id}/edit`)
                }
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Profile Details - Swipeable Cards */}
      <motion.div variants={fadeInVariant} className="mb-4">
        {/* Toggle Expand/Collapse Button */}
        <div className="flex justify-center mb-4">
          <Button
            variant="default"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="bg-secondary text-secondary-foreground hover:bg-secondary/90 w-full max-w-xs py-2 h-auto"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-2" />
                Collapse Profile Details
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-2" />
                Expand Profile Details
              </>
            )}
          </Button>
        </div>

        {/* Card Navigation - Only arrows */}
        <div className="flex justify-end items-center mb-2">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePrevSlide}
              disabled={currentSlide === 0}
              className="h-8 w-8 rounded-full"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleNextSlide}
              disabled={
                currentSlide >=
                (profile?.runningVideoSagittalUrl &&
                (profile?.runningPower !== null ||
                  profile?.groundContactTime !== null ||
                  profile?.verticalOscillationWearable !== null)
                  ? 4
                  : profile?.runningVideoSagittalUrl ||
                    profile?.runningPower !== null ||
                    profile?.groundContactTime !== null ||
                    profile?.verticalOscillationWearable !== null
                  ? 3
                  : 2)
              }
              className="h-8 w-8 rounded-full"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Swipeable Cards Container */}
        <div
          ref={sliderRef}
          className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 -mx-4 px-4 gap-4"
        >
          {/* Card 1: Runner Info */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ProfileDetailCard
              title="Runner Information"
              icon={<UserCircle className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              {isExpanded ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <DataItem
                    label="Age"
                    value={profile.age}
                    unit=" years"
                    icon={CalendarDays}
                  />
                  <DataItem
                    label="Sex/Gender"
                    value={profile.gender}
                    icon={UserCircle}
                  />
                  <DataItem
                    label="Weight"
                    value={profile.weightKg}
                    unit=" kg"
                    icon={Weight}
                  />
                  <DataItem
                    label="Height"
                    value={profile.heightCm}
                    unit=" cm"
                    icon={Ruler}
                  />
                  <DataItem
                    label="Running Goal"
                    value={profile.runningGoal}
                    icon={Target}
                  />
                  <DataItem
                    label="Primary Terrain"
                    value={profile.terrain}
                    icon={Mountain}
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center">
                  <p className="text-sm text-muted-foreground">
                    Click "Expand Profile Details" to view runner information
                  </p>
                </div>
              )}
            </ProfileDetailCard>
          </motion.div>

          {/* Card 2: Foot Measurements */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.05 }}
          >
            <ProfileDetailCard
              title="Foot Measurements"
              icon={<Footprints className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              {isExpanded ? (
                <div className="space-y-4">
                  {/* Foot Measurements */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <DataItem
                      label="Foot Type"
                      value={"Normal"}
                      icon={Footprints}
                    />
                    <DataItem
                      label="Pronation"
                      value={"Neutral"}
                      icon={Footprints}
                    />
                  </div>

                  {/* Foot Dimensions */}
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2">
                      Foot Dimensions
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <DataItem
                        label="Left Foot Length"
                        value={profile.heelToToeLengthLeft}
                        unit=" mm"
                        icon={Ruler}
                      />
                      <DataItem
                        label="Right Foot Length"
                        value={profile.heelToToeLengthRight}
                        unit=" mm"
                        icon={Ruler}
                      />
                      <DataItem
                        label="Left Forefoot Width"
                        value={profile.forefootWidthLeft}
                        unit=" mm"
                        icon={Ruler}
                      />
                      <DataItem
                        label="Right Forefoot Width"
                        value={profile.forefootWidthRight}
                        unit=" mm"
                        icon={Ruler}
                      />
                    </div>
                  </div>

                  {/* Arch Measurements */}
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2">
                      Arch Measurements
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      <DataItem
                        label="Left Arch Length"
                        value={profile.medialArchLengthLeft}
                        unit=" mm"
                        icon={Ruler}
                      />
                      <DataItem
                        label="Right Arch Length"
                        value={profile.medialArchLengthRight}
                        unit=" mm"
                        icon={Ruler}
                      />
                      <DataItem
                        label="Left Arch Height"
                        value={profile.medialArchHeightLeft}
                        unit=" mm"
                        icon={Ruler}
                      />
                      <DataItem
                        label="Right Arch Height"
                        value={profile.medialArchHeightRight}
                        unit=" mm"
                        icon={Ruler}
                      />
                    </div>
                  </div>

                  {/* Foot Images */}
                  {(profile.footImageTopLeftUrl ||
                    profile.footImageTopRightUrl ||
                    profile.footImageMedialLeftUrl ||
                    profile.footImageMedialRightUrl) && (
                    <div>
                      <h4 className="text-xs font-medium text-muted-foreground mb-2">
                        Foot Images
                      </h4>
                      <div className="grid grid-cols-2 gap-2">
                        {profile.footImageTopLeftUrl && (
                          <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                            <StorageMedia
                              storagePath={profile.footImageTopLeftUrl}
                              type="image"
                              aspectRatio="square"
                              className="w-full h-full object-contain"
                              showFakeDataLabel={false}
                            />
                            <p className="text-xs text-center mt-1">Left Top</p>
                          </div>
                        )}
                        {profile.footImageTopRightUrl && (
                          <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                            <StorageMedia
                              storagePath={profile.footImageTopRightUrl}
                              type="image"
                              aspectRatio="square"
                              className="w-full h-full object-contain"
                              showFakeDataLabel={false}
                            />
                            <p className="text-xs text-center mt-1">
                              Right Top
                            </p>
                          </div>
                        )}
                        {profile.footImageMedialLeftUrl && (
                          <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                            <StorageMedia
                              storagePath={profile.footImageMedialLeftUrl}
                              type="image"
                              aspectRatio="square"
                              className="w-full h-full object-contain"
                              showFakeDataLabel={false}
                            />
                            <p className="text-xs text-center mt-1">
                              Left Side
                            </p>
                          </div>
                        )}
                        {profile.footImageMedialRightUrl && (
                          <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                            <StorageMedia
                              storagePath={profile.footImageMedialRightUrl}
                              type="image"
                              aspectRatio="square"
                              className="w-full h-full object-contain"
                              showFakeDataLabel={false}
                            />
                            <p className="text-xs text-center mt-1">
                              Right Side
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center">
                  <p className="text-sm text-muted-foreground">
                    Click "Expand Profile Details" to view foot measurements
                  </p>
                </div>
              )}
            </ProfileDetailCard>
          </motion.div>

          {/* Card 3: Running Habits */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <ProfileDetailCard
              title="Running Habits"
              icon={<Activity className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              {isExpanded ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <DataItem
                    label="Weekly Mileage"
                    value={profile.averageWeeklyKm}
                    unit=" km"
                    icon={Activity}
                  />
                  <DataItem
                    label="Avg. Pace (Easy/Long)"
                    value={profile.averagePaceEasyLong}
                    icon={Clock}
                  />
                  <DataItem
                    label="Avg. Running Cadence"
                    value={profile.averageCadence}
                    unit=" spm"
                    icon={Activity}
                  />
                  <DataItem
                    label="Previous Injuries"
                    value={profile.previousInjuries}
                    icon={ShieldAlert}
                  />
                  <DataItem
                    label="Typical Climate"
                    value={profile.climate}
                    icon={Thermometer}
                  />
                  <DataItem
                    label="Running Experience"
                    value={
                      profile.runningGoal
                        ? profile.runningGoal.includes("marathon")
                          ? "Advanced"
                          : "Intermediate"
                        : "Beginner"
                    }
                    icon={UserCircle}
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center">
                  <p className="text-sm text-muted-foreground">
                    Click "Expand Profile Details" to view running habits
                  </p>
                </div>
              )}
            </ProfileDetailCard>
          </motion.div>

          {/* Card 4: Video Analysis */}
          {profile.runningVideoSagittalUrl && (
            <motion.div
              className="flex-shrink-0 w-full snap-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.15 }}
            >
              <ProfileDetailCard
                title="Video Analysis"
                icon={<Video className="h-5 w-5 mr-2 text-primary" />}
                isExpanded={isExpanded}
              >
                {isExpanded ? (
                  <div className="space-y-4">
                    {/* Videos */}
                    <div className="space-y-2">
                      <div className="aspect-video bg-black/5 rounded-lg overflow-hidden">
                        <StorageMedia
                          storagePath={profile.runningVideoSagittalUrl}
                          type="video"
                          aspectRatio="video"
                          className="w-full h-full object-contain"
                          showFakeDataLabel={false}
                          loopPreview={false}
                        />
                      </div>
                    </div>

                    {/* Sagittal Analysis */}
                    <div>
                      <h4 className="text-xs font-medium text-muted-foreground mb-2">
                        Running Form Analysis
                      </h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <DataItem
                          label="Overstride"
                          value={
                            profile.overstride !== null &&
                            profile.overstride !== undefined
                              ? profile.overstride > 0.5
                                ? "Significant"
                                : profile.overstride > 0.2
                                ? "Moderate"
                                : "Minimal"
                              : "N/A"
                          }
                          icon={Activity}
                        />
                        <DataItem
                          label="Trunk Lean"
                          value={
                            profile.trunkLean !== null &&
                            profile.trunkLean !== undefined
                              ? `${Math.round(profile.trunkLean)}°`
                              : "N/A"
                          }
                          icon={Activity}
                        />
                        <DataItem
                          label="Vertical Oscillation"
                          value={
                            profile.verticalOscillationVideo !== null &&
                            profile.verticalOscillationVideo !== undefined
                              ? `${profile.verticalOscillationVideo} cm`
                              : "N/A"
                          }
                          icon={Activity}
                        />
                        <DataItem
                          label="Knee Flexion"
                          value={
                            profile.kneeFlexionLoading !== null &&
                            profile.kneeFlexionLoading !== undefined
                              ? `${Math.round(profile.kneeFlexionLoading)}°`
                              : "N/A"
                          }
                          icon={Activity}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center text-center">
                    <p className="text-sm text-muted-foreground">
                      Click "Expand Profile Details" to view video analysis
                    </p>
                  </div>
                )}
              </ProfileDetailCard>
            </motion.div>
          )}

          {/* Card 5: Wearable Data */}
          {(profile.runningPower !== null ||
            profile.groundContactTime !== null ||
            profile.verticalOscillationWearable !== null) && (
            <motion.div
              className="flex-shrink-0 w-full snap-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <ProfileDetailCard
                title="Wearable Data"
                icon={<Activity className="h-5 w-5 mr-2 text-primary" />}
                isExpanded={isExpanded}
              >
                {isExpanded ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {profile.runningPower !== null &&
                        profile.runningPower !== undefined && (
                          <DataItem
                            label="Running Power"
                            value={`${profile.runningPower} W`}
                            icon={Activity}
                          />
                        )}
                      {profile.groundContactTime !== null &&
                        profile.groundContactTime !== undefined && (
                          <DataItem
                            label="Ground Contact Time"
                            value={`${profile.groundContactTime} ms`}
                            icon={Activity}
                          />
                        )}
                      {profile.verticalOscillationWearable !== null &&
                        profile.verticalOscillationWearable !== undefined && (
                          <DataItem
                            label="Vertical Oscillation"
                            value={`${profile.verticalOscillationWearable} cm`}
                            icon={Activity}
                          />
                        )}
                      {profile.averageCadence !== null &&
                        profile.averageCadence !== undefined && (
                          <DataItem
                            label="Average Cadence"
                            value={profile.averageCadence}
                            unit=" spm"
                            icon={Activity}
                          />
                        )}
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center text-center">
                    <p className="text-sm text-muted-foreground">
                      Click "Expand Profile Details" to view wearable data
                    </p>
                  </div>
                )}
              </ProfileDetailCard>
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* Recommendations Section */}
      <motion.div variants={fadeInVariant} className="mt-4">
        <Card className="border-border/50 overflow-hidden">
          <div className="p-6">
            <h2 className="text-lg font-medium mb-4">Shoe Recommendations</h2>

            {profile.recommendation ? (
              <div className="space-y-4">
                {/* Top Recommendations */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {profile.recommendation.shoeModel1 && (
                    <Card
                      className="border-primary/20 bg-primary/5 hover:bg-primary/10 transition-colors cursor-pointer"
                      onClick={() =>
                        router.push(
                          `/shoes/${profile.recommendation?.shoeModel1?.id}`
                        )
                      }
                    >
                      <div className="p-4">
                        <Badge className="w-fit mb-1 bg-primary text-primary-foreground">
                          Top Pick
                        </Badge>
                        <h3 className="text-base font-medium">
                          {profile.recommendation.shoeModel1.fullName}
                        </h3>
                        <p className="text-xs text-muted-foreground">
                          {profile.recommendation.shoeModel1.brand}
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {profile.recommendation.explanation1
                            ? profile.recommendation.explanation1.length > 100
                              ? profile.recommendation.explanation1.substring(
                                  0,
                                  100
                                ) + "..."
                              : profile.recommendation.explanation1
                            : "No explanation available"}
                        </p>
                      </div>
                    </Card>
                  )}

                  {profile.recommendation.shoeModel2 && (
                    <Card
                      className="border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-colors cursor-pointer"
                      onClick={() =>
                        router.push(
                          `/shoes/${profile.recommendation?.shoeModel2?.id}`
                        )
                      }
                    >
                      <div className="p-4">
                        <Badge className="w-fit mb-1 bg-secondary text-secondary-foreground">
                          #2
                        </Badge>
                        <h3 className="text-base font-medium">
                          {profile.recommendation.shoeModel2.fullName}
                        </h3>
                        <p className="text-xs text-muted-foreground">
                          {profile.recommendation.shoeModel2.brand}
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {profile.recommendation.explanation2
                            ? profile.recommendation.explanation2.length > 100
                              ? profile.recommendation.explanation2.substring(
                                  0,
                                  100
                                ) + "..."
                              : profile.recommendation.explanation2
                            : "No explanation available"}
                        </p>
                      </div>
                    </Card>
                  )}

                  {profile.recommendation.shoeModel3 && (
                    <Card
                      className="border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-colors cursor-pointer"
                      onClick={() =>
                        router.push(
                          `/shoes/${profile.recommendation?.shoeModel3?.id}`
                        )
                      }
                    >
                      <div className="p-4">
                        <Badge className="w-fit mb-1 bg-secondary text-secondary-foreground">
                          #3
                        </Badge>
                        <h3 className="text-base font-medium">
                          {profile.recommendation.shoeModel3.fullName}
                        </h3>
                        <p className="text-xs text-muted-foreground">
                          {profile.recommendation.shoeModel3.brand}
                        </p>
                        <p className="text-xs text-muted-foreground mt-2">
                          {profile.recommendation.explanation3
                            ? profile.recommendation.explanation3.length > 100
                              ? profile.recommendation.explanation3.substring(
                                  0,
                                  100
                                ) + "..."
                              : profile.recommendation.explanation3
                            : "No explanation available"}
                        </p>
                      </div>
                    </Card>
                  )}
                </div>

                {/* Additional Recommendations */}
                {(profile.recommendation.shoeModel4 ||
                  profile.recommendation.shoeModel5) && (
                  <>
                    <div className="border-t border-border/50 my-4"></div>
                    <h3 className="text-sm font-medium mb-3">
                      Additional Options
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {profile.recommendation.shoeModel4 && (
                        <Card
                          className="border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-colors cursor-pointer"
                          onClick={() =>
                            router.push(
                              `/shoes/${profile.recommendation?.shoeModel4?.id}`
                            )
                          }
                        >
                          <div className="p-4">
                            <Badge className="w-fit mb-1 bg-secondary/80 text-secondary-foreground">
                              #4
                            </Badge>
                            <h3 className="text-base font-medium">
                              {profile.recommendation.shoeModel4.fullName}
                            </h3>
                            <p className="text-xs text-muted-foreground mt-2">
                              {profile.recommendation.explanation4
                                ? profile.recommendation.explanation4.length >
                                  80
                                  ? profile.recommendation.explanation4.substring(
                                      0,
                                      80
                                    ) + "..."
                                  : profile.recommendation.explanation4
                                : "No explanation available"}
                            </p>
                          </div>
                        </Card>
                      )}

                      {profile.recommendation.shoeModel5 && (
                        <Card
                          className="border-border/50 hover:border-primary/20 hover:bg-primary/5 transition-colors cursor-pointer"
                          onClick={() =>
                            router.push(
                              `/shoes/${profile.recommendation?.shoeModel5?.id}`
                            )
                          }
                        >
                          <div className="p-4">
                            <Badge className="w-fit mb-1 bg-secondary/80 text-secondary-foreground">
                              #5
                            </Badge>
                            <h3 className="text-base font-medium">
                              {profile.recommendation.shoeModel5.fullName}
                            </h3>
                            <p className="text-xs text-muted-foreground mt-2">
                              {profile.recommendation.explanation5
                                ? profile.recommendation.explanation5.length >
                                  80
                                  ? profile.recommendation.explanation5.substring(
                                      0,
                                      80
                                    ) + "..."
                                  : profile.recommendation.explanation5
                                : "No explanation available"}
                            </p>
                          </div>
                        </Card>
                      )}
                    </div>
                  </>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <h3 className="text-lg font-medium text-foreground mb-2">
                  No Recommendations Yet
                </h3>
                <p className="text-sm text-muted-foreground max-w-md mb-4">
                  This profile doesn't have any shoe recommendations yet.
                </p>
                <Button
                  onClick={() =>
                    router.push(`/running-profiles/${profile.id}/analyze`)
                  }
                  className="bg-primary text-primary-foreground"
                >
                  Generate Recommendations
                </Button>
              </div>
            )}
          </div>
        </Card>
      </motion.div>

      {/* Profile Metadata */}
      <motion.div
        variants={fadeInVariant}
        className="mt-4 text-xs text-muted-foreground text-center"
      >
        <p>
          Profile ID: {profile.id} • Last updated:{" "}
          {format(new Date(profile.updatedAt), "MMMM d, yyyy")}
        </p>
      </motion.div>
    </motion.div>
  );
}
